# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import os
import sys
from tqdm import tqdm
import logging
from transformers import get_linear_schedule_with_warmup

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config, get_model_config
from data_loader import create_data_loaders
from models import MECPE_Step1_Model
from utils import (
    set_seed, print_time, MetricsCalculator, Step1Loss,
    EarlyStopping, ModelSaver, list_round
)


def setup_logging(log_dir, dataset_name, model_type):
    """设置日志"""
    log_file = os.path.join(log_dir, f'step1_{dataset_name}_{model_type}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def train_epoch(model, dataloader, criterion, optimizer, scheduler, device, logger, args):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_emotion_preds = []
    all_emotion_trues = []
    all_cause_preds = []
    all_cause_trues = []
    all_masks = []
    
    progress_bar = tqdm(dataloader, desc="Training")
    
    for batch_idx, batch in enumerate(progress_bar):
        # 移动数据到设备
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        audio_features = batch['audio_features'].to(device)
        visual_features = batch['visual_features'].to(device)
        y_emotion = batch['y_emotion'].to(device)
        y_cause = batch['y_cause'].to(device)
        doc_len = batch['doc_len'].to(device)
        texts = batch.get('texts', None)  # 原始文本（BERT_doc需要）

        # 前向传播
        outputs = model(input_ids, attention_mask, audio_features, visual_features, doc_len, texts)
        
        # 基于模型输出创建掩码
        emotion_logits = outputs['emotion_logits']  # (batch_size, seq_len, n_emotions)
        batch_size, actual_seq_len = emotion_logits.shape[:2]
        mask = torch.arange(actual_seq_len, device=device).expand(batch_size, actual_seq_len) < doc_len.unsqueeze(1)
        
        # 确保目标标签和输出维度匹配
        y_emotion = y_emotion[:, :actual_seq_len]
        y_cause = y_cause[:, :actual_seq_len]
        
        # 计算损失
        targets = {'y_emotion': y_emotion, 'y_cause': y_cause}
        loss_dict = criterion(outputs, targets, mask)
        loss = loss_dict['total_loss']
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        if args.gradient_clip > 0:
            nn.utils.clip_grad_norm_(model.parameters(), args.gradient_clip)
        
        optimizer.step()
        if scheduler:
            scheduler.step()
        
        # 统计
        total_loss += loss.item()
        
        # 预测结果 - 只收集有效位置
        emotion_preds = torch.argmax(outputs['emotion_logits'], dim=-1)
        cause_preds = torch.argmax(outputs['cause_logits'], dim=-1)
        
        # 使用mask只提取有效位置的预测和真实值
        mask_cpu = mask.cpu()
        all_emotion_preds.extend(emotion_preds.cpu()[mask_cpu].tolist())
        all_emotion_trues.extend(y_emotion.cpu()[mask_cpu].tolist())
        all_cause_preds.extend(cause_preds.cpu()[mask_cpu].tolist())
        all_cause_trues.extend(y_cause.cpu()[mask_cpu].tolist())
        
        # 更新进度条
        if batch_idx % args.log_interval == 0:
            current_lr = scheduler.get_last_lr()[0] if scheduler else args.learning_rate
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'LR': f'{current_lr:.6f}'
            })
    
    # 计算平均损失和指标
    avg_loss = total_loss / len(dataloader)
    
    # 转换为张量进行指标计算
    all_emotion_preds = torch.tensor(all_emotion_preds)
    all_emotion_trues = torch.tensor(all_emotion_trues)
    all_cause_preds = torch.tensor(all_cause_preds)
    all_cause_trues = torch.tensor(all_cause_trues)
    
    # 创建全True的mask，因为我们已经只收集了有效位置
    all_masks = torch.ones_like(all_emotion_preds, dtype=torch.bool)
    
    # 计算指标
    emotion_p, emotion_r, emotion_f1 = MetricsCalculator.calculate_prf(
        all_emotion_preds, all_emotion_trues, all_masks,
        average='macro' if args.use_emocate else 'binary', use_emocate=args.use_emocate
    )
    # 若为emocate多类，使用“排除neutral后的macro-F1”作为主F1
    if args.use_emocate:
        emo_metrics = MetricsCalculator.calculate_emotion_category_f1(
            all_emotion_preds.unsqueeze(0), all_emotion_trues.unsqueeze(0)
        )
        emotion_f1 = float(emo_metrics['macro_f1'])

    cause_p, cause_r, cause_f1 = MetricsCalculator.calculate_prf(
        all_cause_preds, all_cause_trues, all_masks, average='binary'
    )

    if args.use_emocate:
        logger.info(f"Train - Loss: {avg_loss:.4f}, "
                    f"Emotion P/R/F1_exNeutral: {emotion_p:.4f}/{emotion_r:.4f}/{emotion_f1:.4f}, "
                    f"Cause P/R/F1: {cause_p:.4f}/{cause_r:.4f}/{cause_f1:.4f}")
    else:
        logger.info(f"Train - Loss: {avg_loss:.4f}, "
                    f"Emotion P/R/F1: {emotion_p:.4f}/{emotion_r:.4f}/{emotion_f1:.4f}, "
                    f"Cause P/R/F1: {cause_p:.4f}/{cause_r:.4f}/{cause_f1:.4f}")
    
    return {
        'loss': avg_loss,
        'emotion_f1': emotion_f1,
        'cause_f1': cause_f1,
        'emotion_metrics': (emotion_p, emotion_r, emotion_f1),
        'cause_metrics': (cause_p, cause_r, cause_f1)
    }


def evaluate(model, dataloader, criterion, device, logger, args):
    """评估模型"""
    model.eval()
    total_loss = 0
    all_emotion_preds = []
    all_emotion_trues = []
    all_cause_preds = []
    all_cause_trues = []
    all_masks = []
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            # 移动数据到设备
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            audio_features = batch['audio_features'].to(device)
            visual_features = batch['visual_features'].to(device)
            y_emotion = batch['y_emotion'].to(device)
            y_cause = batch['y_cause'].to(device)
            doc_len = batch['doc_len'].to(device)
            texts = batch.get('texts', None)  # 原始文本（BERT_doc需要）

            # 前向传播
            outputs = model(input_ids, attention_mask, audio_features, visual_features, doc_len, texts)
            
            # 基于模型输出创建掩码
            emotion_logits = outputs['emotion_logits']  # (batch_size, seq_len, n_emotions)
            batch_size, actual_seq_len = emotion_logits.shape[:2]
            mask = torch.arange(actual_seq_len, device=device).expand(batch_size, actual_seq_len) < doc_len.unsqueeze(1)
            
            # 确保目标标签和输出维度匹配
            y_emotion = y_emotion[:, :actual_seq_len]
            y_cause = y_cause[:, :actual_seq_len]
            
            # 计算损失
            targets = {'y_emotion': y_emotion, 'y_cause': y_cause}
            loss_dict = criterion(outputs, targets, mask)
            loss = loss_dict['total_loss']
            
            total_loss += loss.item()
            
            # 预测结果 - 只收集有效位置
            emotion_preds = torch.argmax(outputs['emotion_logits'], dim=-1)
            cause_preds = torch.argmax(outputs['cause_logits'], dim=-1)
            
            # 使用mask只提取有效位置的预测和真实值
            mask_cpu = mask.cpu()
            all_emotion_preds.extend(emotion_preds.cpu()[mask_cpu].tolist())
            all_emotion_trues.extend(y_emotion.cpu()[mask_cpu].tolist())
            all_cause_preds.extend(cause_preds.cpu()[mask_cpu].tolist())
            all_cause_trues.extend(y_cause.cpu()[mask_cpu].tolist())
    
    # 计算平均损失和指标
    avg_loss = total_loss / len(dataloader)
    
    # 转换为张量进行指标计算
    all_emotion_preds = torch.tensor(all_emotion_preds)
    all_emotion_trues = torch.tensor(all_emotion_trues)
    all_cause_preds = torch.tensor(all_cause_preds)
    all_cause_trues = torch.tensor(all_cause_trues)
    
    # 创建全True的mask，因为我们已经只收集了有效位置
    all_masks = torch.ones_like(all_emotion_preds, dtype=torch.bool)
    
    # 计算指标
    emotion_p, emotion_r, emotion_f1 = MetricsCalculator.calculate_prf(
        all_emotion_preds, all_emotion_trues, all_masks,
        average='macro' if args.use_emocate else 'binary', use_emocate=args.use_emocate
    )
    # 若为emocate多类，使用“排除neutral后的macro-F1”作为主F1
    if args.use_emocate:
        emo_metrics = MetricsCalculator.calculate_emotion_category_f1(
            all_emotion_preds.unsqueeze(0), all_emotion_trues.unsqueeze(0)
        )
        emotion_f1 = float(emo_metrics['macro_f1'])

    cause_p, cause_r, cause_f1 = MetricsCalculator.calculate_prf(
        all_cause_preds, all_cause_trues, all_masks, average='binary'
    )

    if args.use_emocate:
        logger.info(f"Eval - Loss: {avg_loss:.4f}, "
                    f"Emotion P/R/F1_exNeutral: {emotion_p:.4f}/{emotion_r:.4f}/{emotion_f1:.4f}, "
                    f"Cause P/R/F1: {cause_p:.4f}/{cause_r:.4f}/{cause_f1:.4f}")
    else:
        logger.info(f"Eval - Loss: {avg_loss:.4f}, "
                    f"Emotion P/R/F1: {emotion_p:.4f}/{emotion_r:.4f}/{emotion_f1:.4f}, "
                    f"Cause P/R/F1: {cause_p:.4f}/{cause_r:.4f}/{cause_f1:.4f}")
    
    return {
        'loss': avg_loss,
        'emotion_f1': emotion_f1,
        'cause_f1': cause_f1,
        'emotion_metrics': (emotion_p, emotion_r, emotion_f1),
        'cause_metrics': (cause_p, cause_r, cause_f1)
    }


def main():
    # 解析参数
    config = Config()
    args = config.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    
    # 设置日志
    logger = setup_logging(args.log_dir, args.dataset, args.model_type)
    
    print_time()
    logger.info("开始Step1训练...")
    logger.info(f"数据集: {args.dataset}")
    logger.info(f"模型类型: {args.model_type}")
    logger.info(f"设备: {device}")
    
    # 创建数据加载器
    logger.info("加载数据...")
    train_loader, test_loader, dev_loader, vocab_size = create_data_loaders(
        dataset_name=args.dataset,
        batch_size=args.batch_size,
        stage='step1',
        use_emocate=args.use_emocate
    )
    
    # 更新配置中的词汇表大小
    args.vocab_size = vocab_size
    logger.info(f"实际词汇表大小: {vocab_size}")
    
    logger.info(f"训练集: {len(train_loader)} 批次")
    logger.info(f"测试集: {len(test_loader)} 批次")
    if dev_loader:
        logger.info(f"验证集: {len(dev_loader)} 批次")
    
    # 创建模型
    logger.info("创建模型...")
    model_config = get_model_config(args, 'step1')
    model = MECPE_Step1_Model(model_config).to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"模型参数总数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    # 创建优化器
    if args.model_type == 'bert':
        # BERT使用AdamW
        optimizer = optim.AdamW(
            model.parameters(),
            lr=args.learning_rate,
            weight_decay=args.weight_decay,
            eps=1e-8
        )
        
        # 学习率调度器
        total_steps = len(train_loader) * args.epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=args.warmup_steps,
            num_training_steps=total_steps
        )
    else:
        # BiLSTM使用Adam
        optimizer = optim.Adam(
            model.parameters(),
            lr=args.learning_rate,
            weight_decay=args.weight_decay
        )
        scheduler = None
    
    # 创建损失函数
    criterion = Step1Loss(
        emotion_weight=args.emotion_weight,
        cause_weight=args.cause_weight,
        audio_emotion_weight=args.audio_emotion_weight,
        visual_emotion_weight=args.visual_emotion_weight
    )
    
    # 早停和模型保存
    early_stopping = EarlyStopping(patience=args.patience)
    model_saver = ModelSaver(args.save_dir)
    
    # 加载检查点（如果有）
    start_epoch = 0
    best_f1 = 0
    if args.load_checkpoint:
        start_epoch, metrics = model_saver.load_checkpoint(model, optimizer, args.load_checkpoint)
        if metrics:
            best_f1 = metrics.get('best_f1', 0)
            logger.info(f"从检查点恢复训练，起始轮数: {start_epoch}, 最佳F1: {best_f1}")
    
    # 训练循环
    logger.info("开始训练...")
    for epoch in range(start_epoch, args.epochs):
        logger.info(f"\n=== Epoch {epoch + 1}/{args.epochs} ===")
        
        # 训练
        train_metrics = train_epoch(model, train_loader, criterion, optimizer, scheduler, device, logger, args)
        
        # 评估
        if (epoch + 1) % args.eval_interval == 0:
            if dev_loader:
                eval_metrics = evaluate(model, dev_loader, criterion, device, logger, args)
                eval_f1 = (eval_metrics['emotion_f1'] + eval_metrics['cause_f1']) / 2
            else:
                eval_metrics = evaluate(model, test_loader, criterion, device, logger, args)
                eval_f1 = (eval_metrics['emotion_f1'] + eval_metrics['cause_f1']) / 2
            
            # 保存最佳模型
            is_best = eval_f1 > best_f1
            if is_best:
                best_f1 = eval_f1
                logger.info(f"新的最佳F1: {best_f1:.4f}")
            
            # 保存检查点
            if (epoch + 1) % args.save_interval == 0 or is_best:
                metrics = {
                    'epoch': epoch + 1,
                    'train_metrics': train_metrics,
                    'eval_metrics': eval_metrics,
                    'best_f1': best_f1
                }
                model_saver.save_checkpoint(model, optimizer, epoch + 1, metrics, is_best)
            
            # 早停检查
            if early_stopping(eval_metrics['loss'], model):
                logger.info("早停触发，停止训练")
                break
    
    # 最终测试
    logger.info("\n=== 最终测试 ===")
    model_saver.load_checkpoint(model, filename='best_model.pt')
    test_metrics = evaluate(model, test_loader, criterion, device, logger, args)
    
    logger.info(f"最终测试结果:")
    logger.info(f"  情感检测 - P: {test_metrics['emotion_metrics'][0]:.4f}, "
                f"R: {test_metrics['emotion_metrics'][1]:.4f}, "
                f"F1: {test_metrics['emotion_metrics'][2]:.4f}")
    logger.info(f"  原因检测 - P: {test_metrics['cause_metrics'][0]:.4f}, "
                f"R: {test_metrics['cause_metrics'][1]:.4f}, "
                f"F1: {test_metrics['cause_metrics'][2]:.4f}")
    logger.info(f"  平均F1: {(test_metrics['emotion_f1'] + test_metrics['cause_f1']) / 2:.4f}")
    
    print_time()
    logger.info("Step1训练完成！")


if __name__ == "__main__":
    main()
