# -*- coding: utf-8 -*-

import argparse
import os


class Config:
    """配置类"""
    
    def __init__(self):
        self.parser = argparse.ArgumentParser(description='MECPE PyTorch Implementation')
        self._add_arguments()
    
    def _add_arguments(self):
        # 数据相关
        self.parser.add_argument('--dataset', type=str, default='iemocap',
                                choices=['iemocap', 'meld'], help='数据集名称')
        self.parser.add_argument('--data_dir', type=str, default='./data',
                                help='数据目录')

        # 模型架构
        self.parser.add_argument('--model_type', type=str, default='bilstm',
                                choices=['bilstm', 'bert'], help='模型类型')
        self.parser.add_argument('--hidden_dim', type=int, default=200,
                                help='隐藏层维度')
        self.parser.add_argument('--n_heads', type=int, default=8,
                                help='多头注意力头数')
        self.parser.add_argument('--n_layers', type=int, default=2,
                                help='Transformer层数')

        # 数据维度
        self.parser.add_argument('--vocab_size', type=int, default=30000,
                                help='词汇表大小')
        self.parser.add_argument('--embedding_dim', type=int, default=300,
                                help='词嵌入维度')
        self.parser.add_argument('--position_embedding_dim', type=int, default=50,
                                help='位置嵌入维度')
        self.parser.add_argument('--max_doc_len', type=int, default=35,
                                help='最大对话长度')
        self.parser.add_argument('--max_sen_len', type=int, default=35,
                                help='最大句子长度')

        # 模态选择
        self.parser.add_argument('--use_audio', action='store_true', default=True,
                                help='是否使用音频特征')
        self.parser.add_argument('--use_visual', action='store_true', default=True,
                                help='是否使用视觉特征')
        self.parser.add_argument('--use_emotion_category', action='store_true', default=True,help='是否使用情感类别信息（Step2）')
        self.parser.add_argument('--use_emocate', action='store_true', default=True,help='是否使用细粒度情感分类（多类），否则使用二分类（neutral vs non-neutral）')

        # 训练相关
        self.parser.add_argument('--batch_size', type=int, default=8,
                                help='批次大小')
        self.parser.add_argument('--learning_rate', type=float, default=1e-5,
                                help='学习率')
        self.parser.add_argument('--weight_decay', type=float, default=1e-5,
                                help='权重衰减')
        self.parser.add_argument('--dropout', type=float, default=0.1,
                                help='Dropout率')
        self.parser.add_argument('--epochs', type=int, default=30,
                                help='训练轮数')
        self.parser.add_argument('--warmup_steps', type=int, default=500,
                                help='学习率预热步数')

        # 优化相关
        self.parser.add_argument('--optimizer', type=str, default='adamw',
                                choices=['adam', 'adamw', 'sgd'], help='优化器类型')
        self.parser.add_argument('--scheduler', type=str, default='cosine',
                                choices=['linear', 'cosine', 'plateau'], help='学习率调度器')
        self.parser.add_argument('--warmup_ratio', type=float, default=0.1,
                                help='预热比例')
        self.parser.add_argument('--label_smoothing', type=float, default=0.0,
                                help='标签平滑系数')
        self.parser.add_argument('--focal_loss_alpha', type=float, default=0.25,
                                help='Focal Loss alpha参数')
        self.parser.add_argument('--focal_loss_gamma', type=float, default=2.0,
                                help='Focal Loss gamma参数')
        self.parser.add_argument('--gradient_accumulation_steps', type=int, default=1,
                                help='梯度累积步数')

        # BERT相关
        self.parser.add_argument('--bert_model_name', type=str, default='../roberta',
                                help='BERT模型路径（本地路径）')
        self.parser.add_argument('--freeze_bert', action='store_true', default=False,
                                help='是否冻结BERT参数')
        self.parser.add_argument('--bert_encoding_type', type=str, default='bert_sen',
                                choices=['bert_sen', 'bert_doc'],
                                help='BERT编码方式：bert_sen(每个话语独立编码) 或 bert_doc(拼接对话后按索引提取)')

        # 损失权重
        self.parser.add_argument('--emotion_weight', type=float, default=1.0,
                                help='情感检测损失权重')
        self.parser.add_argument('--cause_weight', type=float, default=1.0,
                                help='原因检测损失权重')
        self.parser.add_argument('--audio_emotion_weight', type=float, default=0.5,
                                help='音频情感损失权重')
        self.parser.add_argument('--visual_emotion_weight', type=float, default=0.5,
                                help='视觉情感损失权重')

        # 训练策略
        self.parser.add_argument('--share_encoder', action='store_true', default=True,
                                help='情感和原因是否共享编码器')
        self.parser.add_argument('--gradient_clip', type=float, default=1.0,
                                help='梯度裁剪阈值')
        self.parser.add_argument('--patience', type=int, default=7,
                                help='早停耐心')

        # 设备和并行
        self.parser.add_argument('--device', type=str, default='cuda',
                                help='设备')
        self.parser.add_argument('--num_workers', type=int, default=4,
                                help='数据加载器工作线程数')

        # 保存和加载
        self.parser.add_argument('--save_dir', type=str, default='./checkpoints',
                                help='模型保存目录')
        self.parser.add_argument('--log_dir', type=str, default='./logs',
                                help='日志目录')
        self.parser.add_argument('--load_checkpoint', type=str, default=None,
                                help='加载检查点路径')

        # Step特定参数
        self.parser.add_argument('--stage', type=str, default='step1',
                                choices=['step1', 'step2'], help='训练阶段')
        self.parser.add_argument('--step1_model_path', type=str, default=None,
                                help='Step1模型路径（用于Step2训练）')
        self.parser.add_argument('--negative_sampling_ratio', type=float, default=1.0,
                                help='Step2负采样比例')
        self.parser.add_argument('--use_predicted_labels', action='store_true', default=True,
                                help='Step2候选对是否使用Step1预测结果（而非真值/全话语）')
        self.parser.add_argument('--step1_pred_dir', type=str, default=None,
                                help='Step1预测结果目录（包含train/dev/test的JSON）')
        self.parser.add_argument('--weight_ratio_cap', type=float, default=4.0,
                                help='Step2类别权重比例上限（正类/负类），避免过度偏向正类')

        # 其他
        self.parser.add_argument('--seed', type=int, default=42,
                                help='随机种子')
        self.parser.add_argument('--log_interval', type=int, default=10,
                                help='日志打印间隔')
        self.parser.add_argument('--eval_interval', type=int, default=1,
                                help='评估间隔（轮数）')
        self.parser.add_argument('--save_interval', type=int, default=5,
                                help='保存间隔（轮数）')
    
    def parse_args(self):
        args = self.parser.parse_args()
        
        # 数据集特定配置
        if args.dataset == 'iemocap':
            args.audio_dim = 100
            args.visual_dim = 100
            if args.use_emocate:
                args.n_emotions = 6  # 细粒度情感分类：anger, fear, happiness, sadness, frustration, surprise
            else:
                args.n_emotions = 2  # 二分类：neutral vs non-neutral
        elif args.dataset == 'meld':
            args.audio_dim = 6373
            args.visual_dim = 4096
            if args.use_emocate:
                args.n_emotions = 7  # 细粒度情感分类：neutral, anger, disgust, fear, joy, sadness, surprise
            else:
                args.n_emotions = 2  # 二分类：neutral vs non-neutral
        
        # 模型特定优化配置
        if args.model_type == 'bilstm':
            args.batch_size = 32
            args.learning_rate = 0.005
            args.epochs = 30
        elif args.model_type == 'bert':
            # BERT优化配置
            if args.stage == 'step1':
                args.batch_size = 16  # 增加batch size
                args.learning_rate = 2e-5  # 提高学习率
                args.epochs = 20  # 增加训练轮数
                args.warmup_ratio = 0.1
                args.weight_decay = 1e-2  # 增加正则化
                args.dropout = 0.2  # 增加dropout防止过拟合
                args.scheduler = 'cosine'
                args.label_smoothing = 0.1  # 添加标签平滑
            else:  # step2
                args.batch_size = 128  # Step2可以用更大的batch size
                args.learning_rate = 0.003
                args.epochs = 20
                args.weight_decay = 1e-3
        
        # 创建目录
        os.makedirs(args.save_dir, exist_ok=True)
        os.makedirs(args.log_dir, exist_ok=True)
        
        return args


def get_model_config(args, stage='step1'):
    """获取模型配置"""
    if stage == 'step1':
        from models import Step1Config
        config = Step1Config(
            model_type=args.model_type,
            hidden_dim=args.hidden_dim,
            n_heads=args.n_heads,
            dropout=args.dropout,
            share_encoder=args.share_encoder,
            vocab_size=args.vocab_size,
            embedding_dim=args.embedding_dim,
            audio_dim=args.audio_dim,
            visual_dim=args.visual_dim,
            max_doc_len=args.max_doc_len,
            max_sen_len=args.max_sen_len,
            use_audio=args.use_audio,
            use_visual=args.use_visual,
            bert_model_name=args.bert_model_name,
            n_emotions=args.n_emotions
        )
    else:  # step2
        from models import Step2Config
        config = Step2Config(
            model_type=args.model_type,
            hidden_dim=args.hidden_dim,
            dropout=args.dropout,
            vocab_size=args.vocab_size,
            embedding_dim=args.embedding_dim,
            audio_dim=args.audio_dim,
            visual_dim=args.visual_dim,
            position_embedding_dim=args.position_embedding_dim,
            max_sen_len=args.max_sen_len,
            use_audio=args.use_audio,
            use_visual=args.use_visual,
            use_emotion_category=args.use_emotion_category,
            bert_model_name=args.bert_model_name,
            n_emotions=args.n_emotions
        )
    
    return config


if __name__ == "__main__":
    # 测试配置
    config = Config()
    args = config.parse_args()
    
    print("配置参数:")
    for key, value in vars(args).items():
        print(f"  {key}: {value}")
    
    print(f"\n数据集: {args.dataset}")
    print(f"音频维度: {args.audio_dim}")
    print(f"视觉维度: {args.visual_dim}")
    print(f"情感类别数: {args.n_emotions}")
