

### 📊 **数据统计对比**

| 数据集   | 对话数    | 话语数     | 情感-原因对 | 视觉特征有效率 | 音频特征有效率 | 文件大小       |
| -------- | --------- | ---------- | ----------- | -------------- | -------------- | -------------- |
| 训练集   | 984       | 9,764      | 6,896       | 71.9%          | 71.9%          | 783.8 MB       |
| 测试集   | 257       | 2,519      | 1,828       | 71.3%          | 71.3%          | 202.4 MB       |
| 验证集   | 110       | 1,069      | 838         | 75.0%          | 75.0%          | 85.5 MB        |
| **总计** | **1,351** | **13,352** | **9,562**   | **72.0%**      | **72.0%**      | **1,071.8 MB** |


### 转换后HDF5格式
```
conversation_1/
├── utterance_IDs           # [1, 2, 3, ...]
├── texts                   # ["Alright, so...", ...]
├── speakers                # ["Chandler", "All", ...]
├── emotions                # ["neutral", "neutral", ...]
├── visual_features         # (n_utterances, 4096)
├── audio_features          # (n_utterances, 6373)
└── emotion_cause_pairs     # ["3_surprise|1", ...]
```

## 多模态特征详情

### 🎬 **视觉特征**
- **维度**: 4096维
- **数据类型**: float64
- **值范围**: [-17.878, 0.000] (负值，可能经过预处理)
- **有效率**: 72.0%

### 🎵 **音频特征**
- **维度**: 6373维
- **数据类型**: float64
- **值范围**: 极大范围，需要归一化
- **有效率**: 72.0%

### 📈 **特征质量**
- 视觉和音频特征的有效性完全一致
- 零向量主要来自ID映射失败的情况
- 特征分布合理，适合深度学习模型使用

## 数据加载示例

### PyTorch数据加载器
```python
import h5py
import torch
from torch.utils.data import Dataset, DataLoader

class MELDMultimodalDataset(Dataset):
    def __init__(self, h5_file_path):
        self.h5_file_path = h5_file_path
        self.conversation_keys = []
        
        with h5py.File(h5_file_path, 'r') as f:
            self.conversation_keys = [k for k in f.keys() 
                                    if k.startswith('conversation_')]
    
    def __getitem__(self, idx):
        conv_key = self.conversation_keys[idx]
        
        with h5py.File(self.h5_file_path, 'r') as f:
            conv = f[conv_key]
            
            return {
                'conversation_id': conv.attrs['conversation_ID'],
                'texts': [t.decode('utf-8') for t in conv['texts'][:]],
                'speakers': [s.decode('utf-8') for s in conv['speakers'][:]],
                'emotions': [e.decode('utf-8') for e in conv['emotions'][:]],
                'visual_features': torch.tensor(conv['visual_features'][:]),
                'audio_features': torch.tensor(conv['audio_features'][:]),
                'emotion_cause_pairs': [p.decode('utf-8').split('|') 
                                      for p in conv['emotion_cause_pairs'][:]]
            }

# 使用示例
train_dataset = MELDMultimodalDataset('meld_train_multimodal.h5')
train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
```

