# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import os
import sys
from tqdm import tqdm
import logging
from transformers import get_linear_schedule_with_warmup, get_cosine_schedule_with_warmup
import math

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config, get_model_config
from data_loader import create_data_loaders
from models import MECPE_Step1_Model
from utils import (
    set_seed, print_time, MetricsCalculator, Step1Loss,
    EarlyStopping, ModelSaver, list_round
)


def setup_logging(log_dir, dataset_name, model_type):
    """设置日志"""
    log_file = os.path.join(log_dir, f'step1_optimized_{dataset_name}_{model_type}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def create_optimizer_and_scheduler(model, args, num_training_steps):
    """创建优化器和学习率调度器"""
    # 分层学习率：BERT层使用较小学习率，其他层使用较大学习率
    if args.model_type == 'bert':
        bert_params = []
        other_params = []
        
        for name, param in model.named_parameters():
            if 'bert' in name.lower():
                bert_params.append(param)
            else:
                other_params.append(param)
        
        optimizer = optim.AdamW([
            {'params': bert_params, 'lr': args.learning_rate},
            {'params': other_params, 'lr': args.learning_rate * 2}  # 其他层使用2倍学习率
        ], weight_decay=args.weight_decay)
    else:
        optimizer = optim.AdamW(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)
    
    # 学习率调度器
    warmup_steps = int(num_training_steps * args.warmup_ratio)
    
    if args.scheduler == 'cosine':
        scheduler = get_cosine_schedule_with_warmup(
            optimizer, num_warmup_steps=warmup_steps, num_training_steps=num_training_steps
        )
    elif args.scheduler == 'linear':
        scheduler = get_linear_schedule_with_warmup(
            optimizer, num_warmup_steps=warmup_steps, num_training_steps=num_training_steps
        )
    else:
        scheduler = None
    
    return optimizer, scheduler


def train_epoch_optimized(model, dataloader, criterion, optimizer, scheduler, device, logger, args):
    """优化的训练一个epoch"""
    model.train()
    total_loss = 0
    all_emotion_preds = []
    all_emotion_trues = []
    all_cause_preds = []
    all_cause_trues = []
    all_masks = []
    
    progress_bar = tqdm(dataloader, desc="Training")
    
    for batch_idx, batch in enumerate(progress_bar):
        # 移动数据到设备
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        audio_features = batch['audio_features'].to(device)
        visual_features = batch['visual_features'].to(device)
        y_emotion = batch['y_emotion'].to(device)
        y_cause = batch['y_cause'].to(device)
        doc_len = batch['doc_len'].to(device)
        texts = batch.get('texts', None)

        # 前向传播
        outputs = model(input_ids, attention_mask, audio_features, visual_features, doc_len, texts)
        
        # 基于模型输出创建掩码
        emotion_logits = outputs['emotion_logits']
        batch_size, actual_seq_len = emotion_logits.shape[:2]
        mask = torch.arange(actual_seq_len, device=device).expand(batch_size, actual_seq_len) < doc_len.unsqueeze(1)
        
        # 确保目标标签和输出维度匹配
        y_emotion = y_emotion[:, :actual_seq_len]
        y_cause = y_cause[:, :actual_seq_len]
        
        # 计算损失
        targets = {'y_emotion': y_emotion, 'y_cause': y_cause}
        loss_dict = criterion(outputs, targets, mask)
        loss = loss_dict['total_loss']
        
        # 梯度累积（如果batch size较小）
        if args.gradient_accumulation_steps > 1:
            loss = loss / args.gradient_accumulation_steps
        
        # 反向传播
        loss.backward()
        
        # 梯度累积和更新
        if (batch_idx + 1) % args.gradient_accumulation_steps == 0:
            # 梯度裁剪
            if args.gradient_clip > 0:
                nn.utils.clip_grad_norm_(model.parameters(), args.gradient_clip)
            
            optimizer.step()
            if scheduler:
                scheduler.step()
            optimizer.zero_grad()
        
        # 统计
        total_loss += loss.item() * args.gradient_accumulation_steps
        
        # 预测结果
        emotion_preds = torch.argmax(outputs['emotion_logits'], dim=-1)
        cause_preds = torch.argmax(outputs['cause_logits'], dim=-1)
        
        # 收集有效位置的预测和真实值
        for i in range(batch_size):
            length = mask[i].sum().item()
            all_emotion_preds.extend(emotion_preds[i][:length].cpu().tolist())
            all_emotion_trues.extend(y_emotion[i][:length].cpu().tolist())
            all_cause_preds.extend(cause_preds[i][:length].cpu().tolist())
            all_cause_trues.extend(y_cause[i][:length].cpu().tolist())
        
        # 更新进度条
        if batch_idx % args.log_interval == 0:
            current_lr = scheduler.get_last_lr()[0] if scheduler else args.learning_rate
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'LR': f'{current_lr:.6f}'
            })
    
    # 计算平均损失和指标
    avg_loss = total_loss / len(dataloader)
    
    # 计算情感检测指标
    emotion_p, emotion_r, emotion_f1 = MetricsCalculator.calculate_prf(
        torch.tensor(all_emotion_preds), torch.tensor(all_emotion_trues), 
        use_emocate=args.use_emocate
    )
    
    # 计算原因检测指标
    cause_p, cause_r, cause_f1 = MetricsCalculator.calculate_prf(
        torch.tensor(all_cause_preds), torch.tensor(all_cause_trues)
    )
    
    logger.info(f"Train - Loss: {avg_loss:.4f}, Emotion P/R/F1_exNeutral: {emotion_p:.4f}/{emotion_r:.4f}/{emotion_f1:.4f}, Cause P/R/F1: {cause_p:.4f}/{cause_r:.4f}/{cause_f1:.4f}")
    
    return {
        'loss': avg_loss,
        'emotion_f1': emotion_f1,
        'cause_f1': cause_f1,
        'avg_f1': (emotion_f1 + cause_f1) / 2
    }


def main():
    # 解析参数
    config = Config()
    args = config.parse_args()
    
    # 添加优化参数
    args.gradient_accumulation_steps = getattr(args, 'gradient_accumulation_steps', 1)
    args.warmup_ratio = getattr(args, 'warmup_ratio', 0.1)
    args.scheduler = getattr(args, 'scheduler', 'cosine')
    args.label_smoothing = getattr(args, 'label_smoothing', 0.1)
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    
    # 设置日志
    logger = setup_logging(args.log_dir, args.dataset, args.model_type)
    
    print_time()
    logger.info("开始Step1优化训练...")
    logger.info(f"数据集: {args.dataset}")
    logger.info(f"模型类型: {args.model_type}")
    logger.info(f"设备: {device}")
    logger.info(f"优化配置: 学习率={args.learning_rate}, 权重衰减={args.weight_decay}, dropout={args.dropout}")
    logger.info(f"调度器: {args.scheduler}, 预热比例: {args.warmup_ratio}")
    
    # 加载数据
    logger.info("加载数据...")
    train_loader, test_loader, dev_loader = create_data_loaders(args, stage='step1')
    
    # 创建模型
    logger.info("创建模型...")
    model_config = get_model_config(args, stage='step1')
    model = MECPE_Step1_Model(model_config).to(device)
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"模型参数总数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    # 计算训练步数
    num_training_steps = len(train_loader) * args.epochs // args.gradient_accumulation_steps
    
    # 创建优化器和调度器
    optimizer, scheduler = create_optimizer_and_scheduler(model, args, num_training_steps)
    
    # 创建损失函数（带标签平滑）
    criterion = Step1Loss(
        emotion_weight=args.emotion_weight,
        cause_weight=args.cause_weight,
        audio_emotion_weight=args.audio_emotion_weight,
        visual_emotion_weight=args.visual_emotion_weight,
        label_smoothing=args.label_smoothing
    )
    
    # 早停和模型保存
    early_stopping = EarlyStopping(patience=args.patience)
    model_saver = ModelSaver(args.save_dir)
    
    logger.info("开始训练...")
    best_f1 = 0
    
    for epoch in range(args.epochs):
        logger.info(f"\nEpoch {epoch+1}/{args.epochs}")
        
        # 训练
        train_metrics = train_epoch_optimized(model, train_loader, criterion, optimizer, scheduler, device, logger, args)
        
        # 验证
        if dev_loader:
            eval_metrics = evaluate_epoch(model, dev_loader, criterion, device, logger, args)
            current_f1 = eval_metrics['avg_f1']
            
            # 保存最佳模型
            if current_f1 > best_f1:
                best_f1 = current_f1
                logger.info(f"新的最佳F1: {best_f1:.4f}")
                model_saver.save_best_model(model, optimizer, epoch, {'best_f1': best_f1})
            
            # 早停检查
            if early_stopping(eval_metrics['loss'], model):
                logger.info("早停触发，停止训练")
                break
        
        # 定期保存检查点
        if (epoch + 1) % args.save_interval == 0:
            model_saver.save_checkpoint(model, optimizer, epoch, train_metrics)
    
    # 最终测试
    if test_loader:
        logger.info("进行最终测试...")
        test_metrics = evaluate_epoch(model, test_loader, criterion, device, logger, args)
        logger.info("最终测试结果:")
        logger.info(f"  情感检测 - P: {test_metrics.get('emotion_precision', 0):.4f}, R: {test_metrics.get('emotion_recall', 0):.4f}, F1: {test_metrics.get('emotion_f1', 0):.4f}")
        logger.info(f"  原因检测 - P: {test_metrics.get('cause_precision', 0):.4f}, R: {test_metrics.get('cause_recall', 0):.4f}, F1: {test_metrics.get('cause_f1', 0):.4f}")
        logger.info(f"  平均F1: {test_metrics.get('avg_f1', 0):.4f}")
    
    print_time()
    logger.info("Step1优化训练完成！")


if __name__ == "__main__":
    main()
