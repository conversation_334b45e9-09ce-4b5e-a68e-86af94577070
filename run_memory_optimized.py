# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess

from model_path_utils import get_roberta_model_path

def run_step1_memory_optimized(dataset='meld', model_type='bert', use_audio=True, use_visual=True,
                              use_emocate=True, bert_encoding_type='bert_doc'):
    """运行内存优化的Step1训练"""
    cmd = [
        sys.executable, 'train_step1.py',
        '--dataset', dataset,
        '--model_type', model_type,
        '--save_dir', './checkpoints/step1_memory_opt',
        '--log_dir', './logs/step1_memory_opt'
    ]

    if model_type == 'bert':
        # BERT内存优化参数
        cmd.extend([
            '--batch_size', '2',  # 极小batch size
            '--gradient_accumulation_steps', '4',  # 梯度累积保持有效batch size=8
            '--learning_rate', '2e-5',
            '--epochs', '25',  # 稍微增加训练轮数补偿小batch size
            '--weight_decay', '5e-3',  # 适度正则化
            '--dropout', '0.15',  # 适度dropout
            '--warmup_ratio', '0.1',
            '--scheduler', 'cosine',
            '--gradient_clip', '1.0',
            '--patience', '7'  # 增加耐心
        ])
        
        # 使用bert_sen编码方式，内存消耗更小
        if bert_encoding_type == 'bert_doc':
            print("⚠️  BERT_doc模式内存消耗较大，建议使用bert_sen模式")
            cmd.extend(['--bert_encoding_type', 'bert_sen'])  # 强制使用bert_sen
        else:
            cmd.extend(['--bert_encoding_type', bert_encoding_type])
    else:
        # BiLSTM参数
        cmd.extend([
            '--batch_size', '32',
            '--learning_rate', '0.003',
            '--epochs', '30',
            '--weight_decay', '1e-3',
            '--dropout', '0.2'
        ])

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emocate:
        cmd.append('--use_emocate')

    print(f"运行内存优化的Step1命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Step1内存优化训练失败!")
        return False
    print("✓ Step1内存优化训练完成")
    return True

def run_step2_memory_optimized(dataset='meld', use_audio=True, use_visual=True,
                              use_emotion_category=True, step1_pred_dir=None):
    """运行内存优化的Step2训练"""
    cmd = [
        sys.executable, 'train_step2.py',
        '--dataset', dataset,
        '--model_type', 'bilstm',
        '--save_dir', './checkpoints/step2_memory_opt',
        '--log_dir', './logs/step2_memory_opt'
    ]

    # Step2内存优化参数
    cmd.extend([
        '--batch_size', '64',  # Step2内存消耗相对较小
        '--learning_rate', '0.003',
        '--epochs', '20',
        '--weight_decay', '1e-3',
        '--dropout', '0.2',
        '--patience', '7',
        '--weight_ratio_cap', '3.0',
        '--negative_sampling_ratio', '1.5'
    ])

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emotion_category:
        cmd.append('--use_emotion_category')

    if step1_pred_dir and os.path.exists(step1_pred_dir):
        cmd.extend(['--use_predicted_labels', '--step1_pred_dir', step1_pred_dir])
        print(f"✓ 使用Step1预测结果: {step1_pred_dir}")
    else:
        print("⚠️  未使用Step1预测结果，将使用所有话语对作为候选")

    print(f"运行内存优化的Step2命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Step2内存优化训练失败!")
        return False
    print("✓ Step2内存优化训练完成")
    return True

def generate_step1_predictions_memory_optimized(dataset='meld', model_type='bert', 
                                               use_emocate=True, bert_encoding_type='bert_sen'):
    """生成内存优化的Step1预测结果"""
    base_dir = './checkpoints/step1_memory_opt'
    best_path = os.path.join(base_dir, 'best_model.pt')
    step1_model_path = None
    if os.path.exists(best_path):
        step1_model_path = best_path
    else:
        if os.path.exists(base_dir):
            candidates = [f for f in os.listdir(base_dir) if f.startswith('checkpoint_epoch_') and f.endswith('.pt')]
            if candidates:
                candidates.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
                step1_model_path = os.path.join(base_dir, candidates[-1])
    
    if not step1_model_path:
        print(f"❌ 找不到内存优化的Step1模型: {best_path}")
        return False
    print(f"✓ 使用内存优化的Step1模型: {step1_model_path}")

    pred_dir = f"./step1_predictions_memory_opt/{dataset}_{model_type}"
    os.makedirs(pred_dir, exist_ok=True)

    cmd = [
        sys.executable, 'generate_step1_predictions.py',
        '--step1_model_path', step1_model_path,
        '--dataset', dataset,
        '--model_type', model_type,
        '--output_dir', pred_dir,
        '--batch_size', '8'  # 预测时也使用较小batch size
    ]

    if use_emocate:
        cmd.append('--use_emocate')
    if model_type == 'bert':
        cmd.extend(['--bert_encoding_type', bert_encoding_type])

    print(f"运行内存优化的预测生成命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ 内存优化的Step1预测生成失败!")
        return False
    print("✓ 内存优化的Step1预测结果生成完成")
    return pred_dir

def main():
    parser = argparse.ArgumentParser(description='MECPE PyTorch 内存优化训练脚本')
    parser.add_argument('--step', type=str, choices=['step1', 'step2', 'both'],
                       default='both', help='运行哪个步骤')
    parser.add_argument('--dataset', type=str, choices=['iemocap', 'meld'],
                       default='meld', help='数据集')
    parser.add_argument('--model_type', type=str, choices=['bilstm', 'bert'],
                       default='bert', help='Step1模型类型')
    parser.add_argument('--use_audio', action='store_true', default=True,
                       help='使用音频特征')
    parser.add_argument('--use_visual', action='store_true', default=True,
                       help='使用视觉特征')
    parser.add_argument('--use_emotion_category', action='store_true', default=True,
                       help='使用情感类别（Step2）')
    parser.add_argument('--use_emocate', action='store_true', default=True,
                       help='使用细粒度情感分类')
    parser.add_argument('--bert_encoding_type', type=str, default='bert_sen',
                       choices=['bert_sen', 'bert_doc'],
                       help='BERT编码方式（内存优化版本推荐bert_sen）')
    parser.add_argument('--skip_step1_prediction', action='store_true', default=False,
                       help='跳过Step1预测生成')

    args = parser.parse_args()

    print("=== MECPE PyTorch 内存优化训练流程 ===")
    print("🧠 内存优化策略:")
    print("  - 使用极小batch size + 梯度累积")
    print("  - 推荐使用bert_sen编码（内存友好）")
    print("  - 适度的正则化参数")
    print("  - 优化的学习率调度")
    print()

    # 检查本地模型路径
    if args.model_type == 'bert':
        model_path = get_roberta_model_path()
        print(f"本地模型路径: {model_path}")
        if not os.path.exists(model_path):
            print("❌ 错误: 找不到本地RoBERTa模型!")
            return
        else:
            print("✓ 本地模型路径检查通过")

    print(f"数据集: {args.dataset}")
    print(f"模型类型: {args.model_type}")
    print(f"BERT编码方式: {args.bert_encoding_type}")
    
    if args.model_type == 'bert' and args.bert_encoding_type == 'bert_doc':
        print("⚠️  警告: bert_doc模式内存消耗较大，将自动切换到bert_sen模式")
        args.bert_encoding_type = 'bert_sen'

    step1_pred_dir = None

    # Step1: 内存优化的情感和原因检测
    if args.step in ['step1', 'both']:
        print("\n=== 第一阶段: 内存优化的情感和原因检测 ===")
        success = run_step1_memory_optimized(args.dataset, args.model_type, args.use_audio, 
                                           args.use_visual, args.use_emocate, args.bert_encoding_type)
        if not success:
            print("❌ Step1内存优化训练失败，终止流程")
            return

    # 生成Step1预测结果
    if args.step in ['step2', 'both']:
        print("\n=== 生成内存优化的Step1预测结果 ===")
        potential_pred_dir = f"./step1_predictions_memory_opt/{args.dataset}_{args.model_type}"
        if args.skip_step1_prediction and os.path.exists(potential_pred_dir):
            print(f"✓ 跳过预测生成，使用已存在的结果: {potential_pred_dir}")
            step1_pred_dir = potential_pred_dir
        else:
            step1_pred_dir = generate_step1_predictions_memory_optimized(args.dataset, args.model_type, 
                                                                       args.use_emocate, args.bert_encoding_type)
            if not step1_pred_dir:
                print("❌ 内存优化的Step1预测生成失败，Step2将使用所有话语对作为候选")
                step1_pred_dir = None

    # Step2: 内存优化的情感-原因对提取
    if args.step in ['step2', 'both']:
        print("\n=== 第二阶段: 内存优化的情感-原因对提取 ===")
        success = run_step2_memory_optimized(args.dataset, args.use_audio, args.use_visual,
                                           args.use_emotion_category, step1_pred_dir)
        if not success:
            print("❌ Step2内存优化训练失败")
            return

    print("\n=== 🎉 内存优化训练流程完成! ===")
    if step1_pred_dir:
        print(f"Step1预测结果保存在: {step1_pred_dir}")
    print("模型检查点保存在: ./checkpoints/")
    print("训练日志保存在: ./logs/")
    print("\n💡 内存优化说明:")
    print("  - 使用了更小的batch size和梯度累积")
    print("  - 推荐使用bert_sen编码方式")
    print("  - 如果仍有内存问题，可以进一步减少batch_size")

if __name__ == "__main__":
    main()
