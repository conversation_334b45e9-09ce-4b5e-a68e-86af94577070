# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess

from model_path_utils import get_roberta_model_path

def run_step1_optimized(dataset='meld', model_type='bert', use_audio=True, use_visual=True,
                       use_emocate=True, bert_encoding_type='bert_doc'):
    """运行优化的Step1训练"""
    cmd = [
        sys.executable, 'train_step1.py',
        '--dataset', dataset,
        '--model_type', model_type,
        '--save_dir', './checkpoints/step1_optimized',
        '--log_dir', './logs/step1_optimized'
    ]

    if model_type == 'bert':
        # BERT优化参数
        cmd.extend([
            '--batch_size', '8',  # 增加batch size
            '--learning_rate', '2e-5',  # 提高学习率
            '--epochs', '20',  # 增加训练轮数
            '--weight_decay', '1e-2',  # 增加正则化
            '--dropout', '0.2',  # 增加dropout
            '--warmup_ratio', '0.1',
            '--scheduler', 'cosine',
            '--label_smoothing', '0.1',  # 标签平滑
            '--gradient_clip', '1.0',
            '--patience', '5'  # 减少早停耐心，防止过拟合
        ])
        if bert_encoding_type == 'bert_doc':
            cmd.extend(['--bert_encoding_type', bert_encoding_type])
    else:
        # BiLSTM优化参数
        cmd.extend([
            '--batch_size', '64',
            '--learning_rate', '0.003',
            '--epochs', '30',
            '--weight_decay', '1e-3',
            '--dropout', '0.3'
        ])

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emocate:
        cmd.append('--use_emocate')

    print(f"运行优化的Step1命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Step1优化训练失败!")
        return False
    print("✓ Step1优化训练完成")
    return True

def run_step2_optimized(dataset='meld', use_audio=True, use_visual=True,
                       use_emotion_category=True, step1_pred_dir=None):
    """运行优化的Step2训练"""
    cmd = [
        sys.executable, 'train_step2.py',
        '--dataset', dataset,
        '--model_type', 'bilstm',  # Step2固定使用bilstm
        '--save_dir', './checkpoints/step2_optimized',
        '--log_dir', './logs/step2_optimized'
    ]

    # Step2优化参数
    cmd.extend([
        '--batch_size', '128',  # 增加batch size
        '--learning_rate', '0.003',  # 调整学习率
        '--epochs', '25',  # 增加训练轮数
        '--weight_decay', '1e-3',
        '--dropout', '0.2',
        '--patience', '7',
        '--weight_ratio_cap', '3.0',  # 降低类别权重比例上限
        '--negative_sampling_ratio', '2.0'  # 增加负采样比例
    ])

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emotion_category:
        cmd.append('--use_emotion_category')

    # 如果提供了Step1预测目录，则使用预测结果
    if step1_pred_dir and os.path.exists(step1_pred_dir):
        cmd.extend(['--use_predicted_labels', '--step1_pred_dir', step1_pred_dir])
        print(f"✓ 使用Step1预测结果: {step1_pred_dir}")
    else:
        print("⚠️  未使用Step1预测结果，将使用所有话语对作为候选")

    print(f"运行优化的Step2命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Step2优化训练失败!")
        return False
    print("✓ Step2优化训练完成")
    return True

def generate_step1_predictions_optimized(dataset='meld', model_type='bert', 
                                       use_emocate=True, bert_encoding_type='bert_doc'):
    """生成优化的Step1预测结果"""
    # 确定Step1模型路径
    base_dir = './checkpoints/step1_optimized'
    best_path = os.path.join(base_dir, 'best_model.pt')
    step1_model_path = None
    if os.path.exists(best_path):
        step1_model_path = best_path
    else:
        # 回退到最近的checkpoint
        if os.path.exists(base_dir):
            candidates = [f for f in os.listdir(base_dir) if f.startswith('checkpoint_epoch_') and f.endswith('.pt')]
            if candidates:
                candidates.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
                step1_model_path = os.path.join(base_dir, candidates[-1])
    
    if not step1_model_path:
        print(f"❌ 找不到优化的Step1模型: {best_path}")
        return False
    print(f"✓ 使用优化的Step1模型: {step1_model_path}")

    # 设置预测结果输出目录
    pred_dir = f"./step1_predictions_optimized/{dataset}_{model_type}"
    os.makedirs(pred_dir, exist_ok=True)

    cmd = [
        sys.executable, 'generate_step1_predictions.py',
        '--step1_model_path', step1_model_path,
        '--dataset', dataset,
        '--model_type', model_type,
        '--output_dir', pred_dir,
        '--batch_size', '32'  # 预测时用更大的batch size
    ]

    if use_emocate:
        cmd.append('--use_emocate')
    if model_type == 'bert' and bert_encoding_type == 'bert_doc':
        cmd.extend(['--bert_encoding_type', bert_encoding_type])

    print(f"运行优化的预测生成命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ 优化的Step1预测生成失败!")
        return False
    print("✓ 优化的Step1预测结果生成完成")
    return pred_dir

def main():
    parser = argparse.ArgumentParser(description='MECPE PyTorch 优化训练脚本')
    parser.add_argument('--step', type=str, choices=['step1', 'step2', 'both'],
                       default='both', help='运行哪个步骤')
    parser.add_argument('--dataset', type=str, choices=['iemocap', 'meld'],
                       default='meld', help='数据集')
    parser.add_argument('--model_type', type=str, choices=['bilstm', 'bert'],
                       default='bert', help='Step1模型类型')
    parser.add_argument('--use_audio', action='store_true', default=True,
                       help='使用音频特征')
    parser.add_argument('--use_visual', action='store_true', default=True,
                       help='使用视觉特征')
    parser.add_argument('--use_emotion_category', action='store_true', default=True,
                       help='使用情感类别（Step2）')
    parser.add_argument('--use_emocate', action='store_true', default=True,
                       help='使用细粒度情感分类')
    parser.add_argument('--bert_encoding_type', type=str, default='bert_doc',
                       choices=['bert_sen', 'bert_doc'],
                       help='BERT编码方式')
    parser.add_argument('--skip_step1_prediction', action='store_true', default=False,
                       help='跳过Step1预测生成')

    args = parser.parse_args()

    print("=== MECPE PyTorch 优化训练流程 ===")
    print("🚀 优化配置:")
    print("  - 增加batch size和学习率")
    print("  - 添加标签平滑和更强正则化")
    print("  - 优化类别不平衡处理")
    print("  - 改进学习率调度策略")
    print()

    # 检查本地模型路径
    if args.model_type == 'bert':
        model_path = get_roberta_model_path()
        print(f"本地模型路径: {model_path}")
        if not os.path.exists(model_path):
            print("❌ 错误: 找不到本地RoBERTa模型!")
            return
        else:
            print("✓ 本地模型路径检查通过")

    print(f"数据集: {args.dataset}")
    print(f"模型类型: {args.model_type}")
    print(f"使用音频: {args.use_audio}")
    print(f"使用视觉: {args.use_visual}")
    print(f"BERT编码方式: {args.bert_encoding_type}")

    step1_pred_dir = None

    # Step1: 优化的情感和原因检测
    if args.step in ['step1', 'both']:
        print("\n=== 第一阶段: 优化的情感和原因检测 ===")
        success = run_step1_optimized(args.dataset, args.model_type, args.use_audio, 
                                    args.use_visual, args.use_emocate, args.bert_encoding_type)
        if not success:
            print("❌ Step1优化训练失败，终止流程")
            return

    # 生成Step1预测结果
    if args.step in ['step2', 'both']:
        print("\n=== 生成优化的Step1预测结果 ===")
        potential_pred_dir = f"./step1_predictions_optimized/{args.dataset}_{args.model_type}"
        if args.skip_step1_prediction and os.path.exists(potential_pred_dir):
            print(f"✓ 跳过预测生成，使用已存在的结果: {potential_pred_dir}")
            step1_pred_dir = potential_pred_dir
        else:
            step1_pred_dir = generate_step1_predictions_optimized(args.dataset, args.model_type, 
                                                                args.use_emocate, args.bert_encoding_type)
            if not step1_pred_dir:
                print("❌ 优化的Step1预测生成失败，Step2将使用所有话语对作为候选")
                step1_pred_dir = None

    # Step2: 优化的情感-原因对提取
    if args.step in ['step2', 'both']:
        print("\n=== 第二阶段: 优化的情感-原因对提取 ===")
        success = run_step2_optimized(args.dataset, args.use_audio, args.use_visual,
                                    args.use_emotion_category, step1_pred_dir)
        if not success:
            print("❌ Step2优化训练失败")
            return

    print("\n=== 🎉 优化训练流程完成! ===")
    if step1_pred_dir:
        print(f"Step1预测结果保存在: {step1_pred_dir}")
    print("模型检查点保存在: ./checkpoints/")
    print("训练日志保存在: ./logs/")
    print("\n📊 预期改进:")
    print("  - Step1情感检测F1: 0.43 → 0.50+")
    print("  - Step2对提取F1: 0.62 → 0.68+")

if __name__ == "__main__":
    main()
