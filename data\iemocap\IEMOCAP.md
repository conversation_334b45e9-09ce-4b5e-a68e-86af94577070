
## 数据集统计

### 📊 **整体统计**
| 数据集   | 对话数  | 话语数    | 情感-原因对 | 文件大小     | 会话范围        |
| -------- | ------- | --------- | ----------- | ------------ | --------------- |
| 训练集   | 120     | 5,810     | 4,486       | 10.92 MB     | Ses01-Ses04     |
| 测试集   | 31      | 1,623     | 1,239       | 3.05 MB      | Ses05           |
| **总计** | **151** | **7,433** | **5,725**   | **13.97 MB** | **Ses01-Ses05** |

### 📈 **数据分布**
- **训练/测试比例**: 3.9:1
- **平均话语数**: 训练集48.4句，测试集52.4句
- **平均情感-原因对**: 训练集37.4个，测试集40.0个
- **会话划分**: 无重叠，完全分离

## 情感分布对比

### 训练集情感分布
| 情感类型    | 数量  | 占比  |
| ----------- | ----- | ----- |
| frustration | 1,468 | 25.3% |
| sadness     | 1,324 | 22.8% |
| anger       | 933   | 16.1% |
| happiness   | 839   | 14.4% |
| fear        | 742   | 12.8% |
| neutral     | 504   | 8.7%  |

### 测试集情感分布
| 情感类型    | 数量 | 占比  |
| ----------- | ---- | ----- |
| sadness     | 384  | 23.7% |
| frustration | 381  | 23.5% |
| fear        | 299  | 18.4% |
| happiness   | 245  | 15.1% |
| anger       | 170  | 10.5% |
| neutral     | 144  | 8.9%  |

## 多模态特征详情

### 🎵 **音频特征**
- **维度**: 100维 (原96维填充)
- **训练集范围**: [-35.941, 33.081]
- **测试集范围**: [-27.734, 33.538]
- **特征已标准化**

### 👁️ **视觉特征**
- **维度**: 100维
- **训练集范围**: [0.000, 0.592]
- **测试集范围**: [0.000, 1.308]
- **特征已标准化**

## 文件结构

### HDF5文件内部结构
```
iemocap_train.h5 / iemocap_test.h5
├── conversation_1/
│   ├── utterance_IDs           # [1, 2, 3, ...]
│   ├── original_utterance_IDs  # ["Ses03M_impro08b_M000", ...]
│   ├── texts                   # ["Hello?", "Oh God...", ...]
│   ├── speakers                # ["M", "F", "M", ...]
│   ├── emotions                # ["sadness", "frustration", ...]
│   ├── emotion_labels          # [2, 5, 2, ...]
│   ├── audio_features          # (n_utterances, 100)
│   ├── visual_features         # (n_utterances, 100)
│   └── emotion_cause_pairs     # ["2_frustration|2", ...]
├── conversation_2/
│   └── ...
└── metadata/
    ├── dataset_name: "IEMOCAP Multimodal ECPE - TRAIN/TEST"
    ├── split: "train/test"
    ├── total_conversations: 120/31
    ├── emotion_categories: 6
    ├── audio_feature_dim: 96
    ├── visual_feature_dim: 100
    └── emotion_0-5: 情感标签映射
```

## 数据加载示例

### PyTorch数据加载器
```python
import h5py
import torch
from torch.utils.data import Dataset, DataLoader

class IEMOCAPDataset(Dataset):
    def __init__(self, h5_file_path):
        self.h5_file_path = h5_file_path
        self.conversation_keys = []
        
        with h5py.File(h5_file_path, 'r') as f:
            self.conversation_keys = [k for k in f.keys() 
                                    if k.startswith('conversation_')]
    
    def __len__(self):
        return len(self.conversation_keys)
    
    def __getitem__(self, idx):
        conv_key = self.conversation_keys[idx]
        
        with h5py.File(self.h5_file_path, 'r') as f:
            conv = f[conv_key]
            
            return {
                'conversation_id': conv.attrs['conversation_ID'],
                'session_name': conv.attrs['session_name'],
                'texts': [t.decode('utf-8') for t in conv['texts'][:]],
                'speakers': [s.decode('utf-8') for s in conv['speakers'][:]],
                'emotions': [e.decode('utf-8') for e in conv['emotions'][:]],
                'emotion_labels': torch.tensor(conv['emotion_labels'][:]),
                'audio_features': torch.tensor(conv['audio_features'][:]),
                'visual_features': torch.tensor(conv['visual_features'][:]),
                'emotion_cause_pairs': [p.decode('utf-8').split('|') 
                                      for p in conv['emotion_cause_pairs'][:]]
            }

# 使用示例
train_dataset = IEMOCAPDataset('iemocap_train.h5')
test_dataset = IEMOCAPDataset('iemocap_test.h5')

train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False)

print(f"训练集: {len(train_dataset)} 个对话")
print(f"测试集: {len(test_dataset)} 个对话")
```
