# PyTorch和相关库
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Transformers和BERT
transformers>=4.30.0
tokenizers>=0.13.0

# 数据处理
h5py>=3.8.0
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# 训练工具
tqdm>=4.65.0
tensorboard>=2.13.0
wandb>=0.15.0  # 可选：用于实验跟踪

# 其他依赖
matplotlib>=3.7.0
seaborn>=0.12.0  # 可选：用于可视化
jupyter>=1.0.0   # 可选：用于数据分析

# 开发工具（可选）
black>=23.0.0    # 代码格式化
flake8>=6.0.0    # 代码检查
pytest>=7.3.0    # 测试框架
